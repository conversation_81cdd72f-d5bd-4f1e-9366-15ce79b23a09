@echo off
echo 手动创建 bivariate_map conda 环境...
echo.

echo 步骤 1: 创建基础环境
conda create -n bivariate_map r-base=4.3.* jupyter -c conda-forge -y

echo.
echo 步骤 2: 激活环境
call conda activate bivariate_map

echo.
echo 步骤 3: 安装 R 内核支持
conda install r-irkernel -c conda-forge -y

echo.
echo 步骤 4: 安装核心 R 包
conda install r-tidyverse r-sf r-raster r-viridis r-cowplot -c conda-forge -y

echo.
echo 步骤 5: 安装系统依赖
conda install gdal proj geos -c conda-forge -y

echo.
echo 步骤 6: 安装其他 R 包
conda install r-magrittr r-rmarkdown r-rstudioapi r-lintr -c conda-forge -y

echo.
echo 环境创建完成！
echo.
echo 要使用环境，请运行：
echo conda activate bivariate_map
echo jupyter notebook
echo.
pause
