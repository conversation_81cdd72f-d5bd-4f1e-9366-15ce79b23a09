# 🎉 Bivariate Map 环境创建完成！

## 环境状态

✅ **基础环境已成功创建**
- 环境名称: `bivariate_map`
- Python 版本: 3.12
- R 版本: 4.4.3
- Jupyter Notebook: 已安装
- R 内核: 已安装

## 立即开始使用

### 1. 激活环境并启动 Jupyter
```bash
conda activate bivariate_map
jupyter notebook
```

### 2. 打开 notebook
在 Jupyter 中打开：`bivariate-maps-ggplot2-sf-master/Bivariate_map.ipynb`

### 3. 选择 R 内核
确保 Jupyter notebook 右上角显示 "R" 内核

## 安装额外的 R 包

如果运行 notebook 时遇到包缺失错误，请运行：

### 方法 1: 使用提供的脚本
```bash
# 双击运行
install_r_packages.bat
```

### 方法 2: 手动安装
```bash
# 激活环境
conda activate bivariate_map

# 安装 R 包
conda install r-tidyverse r-sf r-raster r-viridis r-cowplot r-magrittr r-rmarkdown -c conda-forge -y

# 如果某些包安装失败，在 R 中安装
R -e "install.packages(c('rstudioapi', 'lintr'), repos='https://cran.rstudio.com/')"
```

## 验证环境

运行测试脚本检查环境：
```bash
# 双击运行
test_environment.bat
```

或者在 R 中运行包检查：
```bash
conda activate bivariate_map
R -f check_and_install_packages.R
```

## 所需的 R 包列表

以下是 `Bivariate_map.ipynb` 需要的所有 R 包：

1. **rstudioapi** - RStudio API 接口
2. **tidyverse** - 数据科学包集合（包含 ggplot2, dplyr, tidyr, readr, purrr, tibble）
3. **magrittr** - 管道操作符
4. **lintr** - 代码检查工具
5. **sf** - 空间数据处理
6. **raster** - 栅格数据处理
7. **viridis** - 颜色调色板
8. **cowplot** - ggplot 图形组合
9. **rmarkdown** - R Markdown 支持

## 故障排除

### 如果 R 内核未显示
```bash
conda activate bivariate_map
R -e "IRkernel::installspec()"
```

### 如果包安装失败
1. 检查网络连接
2. 尝试使用不同的 CRAN 镜像
3. 在 R 中手动安装：
```r
install.packages("包名", repos="https://cran.rstudio.com/")
```

### 如果遇到空间数据包问题
某些空间数据包可能需要系统依赖，如果遇到问题：
```bash
conda install gdal proj geos -c conda-forge
```

## 下一步

1. 激活环境：`conda activate bivariate_map`
2. 启动 Jupyter：`jupyter notebook`
3. 打开 notebook 文件
4. 开始运行代码！

## 文件说明

- `bivariate_map_environment.yml` - 完整环境配置文件
- `bivariate_map_simple.yml` - 简化环境配置文件
- `create_bivariate_env.bat/sh` - 自动创建环境脚本
- `install_r_packages.bat` - 安装 R 包脚本
- `test_environment.bat` - 环境测试脚本
- `check_and_install_packages.R` - R 包检查和安装脚本
- `README_环境配置.md` - 详细配置指南

祝您使用愉快！🚀
