# 检查和安装 Bivariate_map.ipynb 所需的 R 包

# 所需的包列表
required_packages <- c(
  "rstudioapi",
  "tidyverse", 
  "magrittr",
  "lintr",
  "sf",
  "raster",
  "viridis",
  "cowplot",
  "rmarkdown"
)

# 检查包是否已安装的函数
check_package <- function(pkg) {
  if (require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("✓", pkg, "已安装并可正常加载\n")
    return(TRUE)
  } else {
    cat("✗", pkg, "未安装或无法加载\n")
    return(FALSE)
  }
}

# 安装缺失包的函数
install_missing_packages <- function(packages) {
  missing_packages <- c()
  
  cat("检查已安装的包...\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")
  
  for (pkg in packages) {
    if (!check_package(pkg)) {
      missing_packages <- c(missing_packages, pkg)
    }
  }
  
  if (length(missing_packages) > 0) {
    cat("\n需要安装的包:", paste(missing_packages, collapse = ", "), "\n")
    cat("正在安装缺失的包...\n")
    
    # 首先尝试从 CRAN 安装
    for (pkg in missing_packages) {
      cat("安装", pkg, "...\n")
      tryCatch({
        install.packages(pkg, dependencies = TRUE, repos = "https://cran.rstudio.com/")
        cat("✓", pkg, "安装成功\n")
      }, error = function(e) {
        cat("✗", pkg, "安装失败:", e$message, "\n")
      })
    }
  } else {
    cat("\n所有必需的包都已安装！\n")
  }
}

# 主函数
main <- function() {
  cat("Bivariate Map 环境包检查工具\n")
  cat(paste(rep("=", 40), collapse = ""), "\n")
  
  # 检查和安装包
  install_missing_packages(required_packages)
  
  cat("\n最终检查...\n")
  cat(paste(rep("=", 20), collapse = ""), "\n")
  
  all_installed <- TRUE
  for (pkg in required_packages) {
    if (!check_package(pkg)) {
      all_installed <- FALSE
    }
  }
  
  if (all_installed) {
    cat("\n🎉 所有包都已正确安装！您可以运行 Bivariate_map.ipynb 了。\n")
  } else {
    cat("\n⚠️  仍有一些包未正确安装。请检查错误信息并手动安装。\n")
  }
  
  # 显示 R 和系统信息
  cat("\nR 版本信息:\n")
  print(R.version.string)
  
  cat("\n系统信息:\n")
  print(Sys.info()[c("sysname", "release", "machine")])
}

# 运行主函数
main()
