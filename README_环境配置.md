# Bivariate Map 环境配置指南

本指南将帮助您创建一个 conda 环境来运行 `Bivariate_map.ipynb` 中的 R 代码。

## 环境要求

该 Jupyter notebook 需要以下 R 包：

- **rstudioapi**: RStudio API 接口
- **tidyverse**: 数据科学包集合（包含 ggplot2, dplyr, tidyr, readr, purrr, tibble）
- **magrittr**: 管道操作符
- **lintr**: 代码检查工具
- **sf**: 空间数据处理
- **raster**: 栅格数据处理
- **viridis**: 颜色调色板
- **cowplot**: ggplot 图形组合
- **rmarkdown**: R Markdown 支持

## 快速开始

### ✅ 基础环境已创建成功！

基础的 `bivariate_map` conda 环境已经创建完成，包含：
- Python 3.12
- R 4.4.3
- Jupyter Notebook
- R 内核支持

### 立即使用

1. **激活环境**：
```bash
conda activate bivariate_map
```

2. **启动 Jupyter Notebook**：
```bash
jupyter notebook
```

3. **在 Jupyter 中选择 R 内核**：
   - 打开 `bivariate-maps-ggplot2-sf-master/Bivariate_map.ipynb`
   - 确保右上角显示 "R" 内核

### 安装额外的 R 包

由于网络原因，一些 R 包需要在激活环境后手动安装：

```bash
# 激活环境
conda activate bivariate_map

# 安装核心 R 包
conda install r-tidyverse r-sf r-raster r-viridis r-cowplot r-magrittr r-rmarkdown -c conda-forge -y

# 如果 conda 安装失败，可以在 R 中安装
R -e "install.packages(c('rstudioapi', 'lintr'), repos='https://cran.rstudio.com/')"
```

## 使用说明

1. **激活环境**：
   ```bash
   conda activate bivariate_map
   ```

2. **启动 Jupyter**：
   ```bash
   jupyter notebook
   ```

3. **在 Jupyter 中**：
   - 打开 `bivariate-maps-ggplot2-sf-master/Bivariate_map.ipynb`
   - 确保选择 R 内核（右上角应显示 "R"）
   - 运行代码单元格

## 环境包含的组件

### R 语言核心
- R 4.3.x
- R essentials

### Jupyter 支持
- Jupyter Notebook
- R 内核支持

### 空间数据处理
- GDAL: 地理空间数据抽象库
- PROJ: 地图投影库
- GEOS: 几何引擎
- UDUNITS2: 单位转换库

### 开发工具
- devtools: R 包开发工具
- knitr: 动态报告生成

## 故障排除

### 常见问题

1. **R 内核未显示**：
   ```bash
   # 重新安装 R 内核
   conda activate bivariate_map
   R -e "IRkernel::installspec()"
   ```

2. **空间数据包安装失败**：
   - 确保系统已安装 GDAL、PROJ 等依赖
   - 在 Windows 上可能需要额外配置

3. **包版本冲突**：
   ```bash
   # 更新环境
   conda env update -f bivariate_map_environment.yml
   ```

### 验证安装

在 R 中运行以下代码验证所有包是否正确安装：

```r
# 检查所有必需的包
required_packages <- c("rstudioapi", "tidyverse", "magrittr", 
                      "lintr", "sf", "raster", "viridis", 
                      "cowplot", "rmarkdown")

for(pkg in required_packages) {
  if(require(pkg, character.only = TRUE)) {
    cat(pkg, "✓ 已安装\n")
  } else {
    cat(pkg, "✗ 未安装\n")
  }
}
```

## 环境管理

### 删除环境
```bash
conda env remove -n bivariate_map
```

### 导出环境
```bash
conda env export -n bivariate_map > my_environment.yml
```

### 更新环境
```bash
conda env update -f bivariate_map_environment.yml
```

## 注意事项

1. 首次创建环境可能需要较长时间，请耐心等待
2. 确保网络连接稳定，以便下载所需包
3. 如果遇到权限问题，可能需要管理员权限
4. 建议定期更新环境以获取最新的包版本

## 支持

如果遇到问题，请检查：
1. conda 是否正确安装
2. 网络连接是否正常
3. 是否有足够的磁盘空间
4. 系统是否满足依赖要求
