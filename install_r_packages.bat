@echo off
echo 安装 Bivariate_map.ipynb 所需的 R 包...
echo.

echo 激活 bivariate_map 环境...
call conda activate bivariate_map

echo.
echo 步骤 1: 安装核心 R 包（通过 conda）
conda install r-tidyverse r-sf r-raster r-viridis r-cowplot r-magrittr r-rmarkdown -c conda-forge -y

echo.
echo 步骤 2: 安装其他 R 包（通过 R）
R -e "install.packages(c('rstudioapi', 'lintr'), repos='https://cran.rstudio.com/', dependencies=TRUE)"

echo.
echo 步骤 3: 运行包检查脚本
R -f check_and_install_packages.R

echo.
echo R 包安装完成！
echo.
echo 现在可以运行: jupyter notebook
echo 然后打开 bivariate-maps-ggplot2-sf-master/Bivariate_map.ipynb
echo.
pause
